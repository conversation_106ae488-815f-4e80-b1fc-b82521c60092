// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

import ajax from 'core/ajax';

const { call: fetchMany } = ajax;

// Repository functions for payment flow (keep these for payment functionality)
const applyCoupon = (couponinput, instanceid) =>
    fetchMany([{ methodname: "moodle_stripepaymentpro_applycoupon", args: { couponinput, instanceid } }])[0];

const stripeenrol = (userid, couponid, instanceid) =>
    fetchMany([{ methodname: "moodle_stripepaymentpro_stripe_enrol", args: { userid, couponid, instanceid } }])[0];

const createDOM = (instanceid) => {
    const cache = new Map();
    return {
        getelement(id) {
            const fullid = `${id}-${instanceid}`;
            if (!cache.has(fullid)) {
                cache.set(fullid, document.getElementById(fullid));
            }
            return cache.get(fullid);
        },
        setelement(id, html) {
            const element = this.getelement(id);
            if (element) {
                element.innerHTML = html;
            }
        },
        toggleelement(id, show) {
            const element = this.getelement(id);
            if (element) {
                element.style.display = show ? "block" : "none";
            }
        },
        focuselement(id) {
            const element = this.getelement(id);
            if (element) {
                element.focus();
            }
        },
        setbutton(id, disabled, text, opacity = disabled ? "0.7" : "1") {
            const button = this.getelement(id);
            if (button) {
                button.disabled = disabled;
                button.textContent = text;
                button.style.opacity = opacity;
                button.style.cursor = disabled ? "not-allowed" : "pointer";
            }
        },
    };
};

function stripe_payment_pro(userid, couponid, instanceid,publishablekey, pleasewaitstring, entercoupon, couponappling, paymenterror, paymentgatewaytype) {
    const DOM = createDOM(instanceid);
    let checkoutInstance;
    if (typeof window.Stripe === "undefined") {
        // Display an error or log if Stripe.js is not loaded
        displayMessage("paymentresponse", "Stripe.js library not loaded. Please check your template includes.", "error");
        console.error("Stripe.js not loaded.");
        return;
    }

    const displayMessage = (containerid, message, type) => {
        let color;
        switch (type) {
            case "error": color = "red"; break;
            case "success": color = "green"; break;
            default: color = "blue"; break;
        }
        DOM.setelement(containerid, `<p style="color: ${color}; font-weight: bold;">${message}</p>`);
        DOM.toggleelement(containerid, true);
    };

    const clearError = (containerId) => {
        DOM.setelement(containerId, "");
        DOM.toggleelement(containerId, false);
    };

    const updateUIFromServerResponse = (data) => {
        if (data.message) {
            displayMessage("showmessage", data.message, data.uistate === "error" ? "error" : "success");
        } else {
            clearError("showmessage");
        }

        DOM.toggleelement("enrolbutton", data.uistate === "paid");
        DOM.toggleelement("total", data.uistate === "paid");

        if (data.uistate !== "error") {
            DOM.toggleelement("discountsection", data.showsections.discountsection);

            if (data.showsections.discountsection) {
                if (data.couponname) {
                    DOM.setelement("discounttag", data.couponname);
                }
                if (data.discountamount) {
                    // Use currency symbol ($ for USD) instead of currency code
                    const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                    DOM.setelement("discountamountdisplay", `-${currencySymbol}${data.discountamount}`);
                }
                if (data.discountamount && data.discountvalue) {
                    const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                    let note = data.coupontype === "percentoff"
                        ? `${data.discountvalue}% off`
                        : `${currencySymbol}${data.discountvalue} off`;

                    // Add duration information if available
                    if (data.couponduration) {
                        if (data.couponduration === "repeating" && data.coupondurationmonths) {
                            note += ` Expires in ${data.coupondurationmonths} months`;
                        } else if (data.couponduration !== "once") {
                            note += ` ${data.couponduration}`;
                        }
                    }

                    DOM.setelement("discountnote", note);
                }
            }

            if (data.status && data.currency) {
                const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                const formattedAmount = `${currencySymbol}${parseFloat(data.status).toFixed(2)}`;

                // Update main price display
                const mainprice = DOM.getelement("mainprice");
                if (mainprice) {
                    mainprice.textContent = formattedAmount;
                }

                // Update total amount display
                const totalamount = DOM.getelement("totalamount");
                if (totalamount) {
                    totalamount.textContent = formattedAmount;
                }
            }
        }
    };

    const applyCouponHandler = async (event) => {
        event.preventDefault();
        const couponinput = DOM.getelement("coupon");
        const couponcode = couponinput?.value.trim();
        if (!couponcode) {
            displayMessage("showmessage", entercoupon, "error");
            DOM.focuselement("coupon");
            return;
        }
        DOM.setbutton("apply", true, couponappling);
        try {
            const data = await applyCoupon(couponcode, instanceid);
            if (data?.status !== undefined) {
                couponid = couponcode;
                DOM.toggleelement("coupon", false);
                DOM.toggleelement("apply", false);
                updateUIFromServerResponse(data);
            } else {
                throw new Error("Invalid server response");
            }
        } catch (error) {
            displayMessage("showmessage", error.message || "Coupon validation failed", "error");
            DOM.focuselement("coupon");
        } finally {
            DOM.setbutton("apply", false, "Apply"); // Ensure button is re-enabled
        }
    };

    const EnrollHandler = async () => {
        const enrollbutton = DOM.getelement("enrolbutton");
        if (!enrollbutton) return;
        clearError("paymentresponse");
        DOM.setbutton("enrolbutton", true, pleasewaitstring);

        try {
            const paymentdata = await stripeenrol(userid, couponid, instanceid);
            if (paymentdata.error?.message) {
                displayMessage("paymentresponse", paymentdata.error.message, "error");
            } else if (paymentdata.status === "success" && paymentdata.redirecturl) {
                window.location.href = paymentdata.redirecturl;
            } else {
                displayMessage("paymentresponse", "Unknown error occurred during payment.", "error");
            }
        } catch (err) {
            displayMessage("paymentresponse", err.message, "error");
        } finally {
            DOM.toggleelement("enrolbutton", false);
        }
    };

    const setupEventListeners = () => {
        const elements = [
            { id: "apply", event: "click", handler: applyCouponHandler },
            { id: "enrolbutton", event: "click", handler: EnrollHandler },
        ];
        elements.forEach(({ id, event, handler }) => {
            const element = DOM.getelement(id);
            if (element) {
                element.addEventListener(event, handler);
            }
        });
    };

    if (paymentgatewaytype === "elements") {
        DOM.toggleelement("enrolbutton", false);
        const stripe = Stripe(publishablekey);

        const initializeElements = async () => {
            const paymentelementContainer = DOM.getelement("payment-element");
            if (!paymentelementContainer) {
                displayMessage("paymentresponse", "Payment element container (ID: payment-element) not found in HTML. Check your template.", "error");
                // DOM.setbutton("enrolbutton", false, "Buy Now"); // Ensure button is visible but shows error
                return;
            }
            clearError("paymentresponse"); // Clear any previous errors before initializing

            try {
                // This fetches the client_secret from your Moodle server
                const response = await stripeenrol(userid, couponid, instanceid);
                const data = JSON.parse(response.paymentintent);
                let clientSecret = data.client_secret;

                if (!clientSecret) {
                    displayMessage("paymentresponse", "Failed to get a valid client secret for payment initialization. Check server logs.", "error");
                    return;
                }
                clientSecret = decodeURIComponent(clientSecret);
                const elements = stripe.elements({ clientSecret });
                const paymentElement = elements.create("payment");
                paymentElement.mount(`#${paymentelementContainer.id}`);
                // Initialize and mount the embedded checkout
                checkoutInstance = { stripe, elements };

                // Once mounted, show the "Buy Now" button. It will now trigger checkoutInstance.confirm()
                DOM.toggleelement("enrolbutton", true);
                DOM.setbutton("enrolbutton", false, "Buy Now"); // Ensure it's not disabled, ready for click

            } catch (err) {
                console.error("Stripe Elements initialization error:", err);
                displayMessage("paymentresponse", err.message || "Stripe initialization error. Check console.", "error");
                // DOM.setbutton("enrolbutton", false, "Buy Now"); // Re-enable button on error
            }
        };
        initializeElements(); // Call this function immediately when paymentgatewaytype is 'elements'
    } else {
        // If not 'elements' mode, ensure the original enrol button is visible and functional
        DOM.toggleelement("enrolbutton", true);
        DOM.setbutton("enrolbutton", false, "Buy Now"); // Ensure it's enabled
    }

    setupEventListeners();
}

/**
 * Initialize coupon settings for the coupon management page
 */
const initCouponSettings = () => {
    console.log('Coupon settings initialized');

    // Add search functionality only (deletion is handled by template functions)
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer && !document.querySelector('#coupon-search')) {
        const searchContainer = document.createElement('div');
        searchContainer.style.marginBottom = '15px';
        searchContainer.innerHTML = `
            <input type="text" id="coupon-search" placeholder="Search coupons..."
                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">
        `;
        tableContainer.parentNode.insertBefore(searchContainer, tableContainer);

        document.getElementById('coupon-search').addEventListener('input', (event) => {
            const searchTerm = event.target.value.toLowerCase();
            document.querySelectorAll('.table tbody tr').forEach(row => {
                row.style.display = row.textContent.toLowerCase().includes(searchTerm) ? '' : 'none';
            });
        });
    }
};

export default {
    stripe_payment_pro,
    initCouponSettings
};
