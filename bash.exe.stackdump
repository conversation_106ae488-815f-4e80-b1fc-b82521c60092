Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006116E (00021028DEE8, 000210272B3E, 0007FFFFB6A0, 0007FFFFA5A0) msys-2.0.dll+0x2116E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 0007FFFFB6A0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFB6A0  00021006A525 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC22070000 ntdll.dll
7FFC203F0000 KERNEL32.DLL
7FFC1F3E0000 KERNELBASE.dll
7FFC20980000 USER32.dll
7FFC1FBA0000 win32u.dll
7FFC21460000 GDI32.dll
7FFC1FBD0000 gdi32full.dll
7FFC1F8E0000 msvcp_win.dll
7FFC1F7C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC21F60000 advapi32.dll
7FFC207C0000 msvcrt.dll
7FFC20030000 sechost.dll
7FFC1F980000 bcrypt.dll
7FFC20230000 RPCRT4.dll
7FFC1E9E0000 CRYPTBASE.DLL
7FFC1F220000 bcryptPrimitives.dll
7FFC20690000 IMM32.DLL
