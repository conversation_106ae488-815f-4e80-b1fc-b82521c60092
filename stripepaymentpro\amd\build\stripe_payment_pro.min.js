define(["core/ajax"],function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=t(e);const{call:o}=n.default,r=(e,t,n)=>o([{methodname:"moodle_stripepaymentpro_stripe_enrol",args:{userid:e,couponid:t,instanceid:n}}])[0],s=e=>{const t=new Map;return{getelement(n){const o=`${n}-${e}`;return t.has(o)||t.set(o,document.getElementById(o)),t.get(o)},setelement(e,t){const n=this.getelement(e);n&&(n.innerHTML=t)},toggleelement(e,t){const n=this.getelement(e);n&&(n.style.display=t?"block":"none")},focuselement(e){const t=this.getelement(e);t&&t.focus()},setbutton(e,t,n,o=(t?"0.7":"1")){const r=this.getelement(e);r&&(r.disabled=t,r.textContent=n,r.style.opacity=o,r.style.cursor=t?"not-allowed":"pointer")}}};return{stripe_payment_pro:function(e,t,n,i,c,l,a,u,p){const d=s(n);let m;if(void 0===window.Stripe)return y("paymentresponse","Stripe.js library not loaded. Please check your template includes.","error"),void console.error("Stripe.js not loaded.");const y=(e,t,n)=>{let o;switch(n){case"error":o="red";break;case"success":o="green";break;default:o="blue"}d.setelement(e,`<p style="color: ${o}; font-weight: bold;">${t}</p>`),d.toggleelement(e,!0)},g=e=>{d.setelement(e,""),d.toggleelement(e,!1)},f=async e=>{e.preventDefault();const r=d.getelement("coupon"),s=r?.value.trim();if(!s)return y("showmessage",l,"error"),void d.focuselement("coupon");d.setbutton("apply",!0,a);try{const e=await((e,t)=>o([{methodname:"moodle_stripepaymentpro_applycoupon",args:{couponinput:e,instanceid:t}}])[0])(s,n);if(void 0===e?.status)throw new Error("Invalid server response");t=s,d.toggleelement("coupon",!1),d.toggleelement("apply",!1),(e=>{if(e.message?y("showmessage",e.message,"error"===e.uistate?"error":"success"):g("showmessage"),d.toggleelement("enrolbutton","paid"===e.uistate),d.toggleelement("total","paid"===e.uistate),"error"!==e.uistate){if(d.toggleelement("discountsection",e.showsections.discountsection),e.showsections.discountsection){if(e.couponname&&d.setelement("discounttag",e.couponname),e.discountamount){const t="USD"===e.currency?"$":e.currency+" ";d.setelement("discountamountdisplay",`-${t}${e.discountamount}`)}if(e.discountamount&&e.discountvalue){const t="USD"===e.currency?"$":e.currency+" ";let n="percentoff"===e.coupontype?`${e.discountvalue}% off`:`${t}${e.discountvalue} off`;e.couponduration&&("repeating"===e.couponduration&&e.coupondurationmonths?n+=` Expires in ${e.coupondurationmonths} months`:"once"!==e.couponduration&&(n+=` ${e.couponduration}`)),d.setelement("discountnote",n)}}if(e.status&&e.currency){const t=`${"USD"===e.currency?"$":e.currency+" "}${parseFloat(e.status).toFixed(2)}`,n=d.getelement("mainprice");n&&(n.textContent=t);const o=d.getelement("totalamount");o&&(o.textContent=t)}}})(e)}catch(e){y("showmessage",e.message||"Coupon validation failed","error"),d.focuselement("coupon")}finally{d.setbutton("apply",!1,"Apply")}},h=async()=>{if(d.getelement("enrolbutton")){g("paymentresponse"),d.setbutton("enrolbutton",!0,c);try{const o=await r(e,t,n);o.error?.message?y("paymentresponse",o.error.message,"error"):"success"===o.status&&o.redirecturl?window.location.href=o.redirecturl:y("paymentresponse","Unknown error occurred during payment.","error")}catch(e){y("paymentresponse",e.message,"error")}finally{d.toggleelement("enrolbutton",!1)}}};if("elements"===p){d.toggleelement("enrolbutton",!1);const o=Stripe(i);(async()=>{const s=d.getelement("payment-element");if(s){g("paymentresponse");try{const i=await r(e,t,n);let c=JSON.parse(i.paymentintent).client_secret;if(!c)return void y("paymentresponse","Failed to get a valid client secret for payment initialization. Check server logs.","error");c=decodeURIComponent(c);const l=o.elements({clientSecret:c});l.create("payment").mount(`#${s.id}`),m={stripe:o,elements:l},d.toggleelement("enrolbutton",!0),d.setbutton("enrolbutton",!1,"Buy Now")}catch(e){console.error("Stripe Elements initialization error:",e),y("paymentresponse",e.message||"Stripe initialization error. Check console.","error")}}else y("paymentresponse","Payment element container (ID: payment-element) not found in HTML. Check your template.","error")})()}else d.toggleelement("enrolbutton",!0),d.setbutton("enrolbutton",!1,"Buy Now");[{id:"apply",event:"click",handler:f},{id:"enrolbutton",event:"click",handler:h}].forEach(({id:e,event:t,handler:n})=>{const o=d.getelement(e);o&&o.addEventListener(t,n)})},initCouponSettings:()=>{console.log("Coupon settings initialized");const e=document.querySelector(".table-responsive");if(e&&!document.querySelector("#coupon-search")){const t=document.createElement("div");t.style.marginBottom="15px",t.innerHTML='\n            <input type="text" id="coupon-search" placeholder="Search coupons..."\n                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">\n        ',e.parentNode.insertBefore(t,e),document.getElementById("coupon-search").addEventListener("input",e=>{const t=e.target.value.toLowerCase();document.querySelectorAll(".table tbody tr").forEach(e=>{e.style.display=e.textContent.toLowerCase().includes(t)?"":"none"})})}}}});
