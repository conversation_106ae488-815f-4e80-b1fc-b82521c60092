{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepaymentpro/enrol_page

    Contents of the enrolment widget on the course enrolment page

    Example context (json):
    {
            'currency' => "USD",
            'currency_symbol' => "$",
            'cost' => "20",
            'total_cost' => "20",
            'sign_up_fee' => "10",
            'renewal_fee' => "10",
            'coursename' => "python 101",
            'instanceid' => "$instance->id",
            'enrolbtncolor' => "#fffff",
            'enable_coupon_section' => true,
            'has_recurring' => true,
            'interval' => "days",
            'interval_count' => "7",
    }
}}
<script src="https://js.stripe.com/v3/"></script>
<div class="paydetail wrapper">
          <p class="heading">Subscribe to Enrolment in course {{coursename}} </p>
           <div class="price-row">
                <div class="price" id="mainprice-{{instanceid}}">
                	{{currency_symbol}}{{total_cost}}
                </div>
                <div class="duration">
                	<span> due </span>
                	<span> today </span>
                </div>
           </div>
           <p class="heading"> Then {{currency_symbol}}{{renewal_fee}} every {{interval_count}} {{interval}} </p>

           <ul>
                <li class="total">
                	<div class="details">
                		<div class="name"> Enrolment in course {{coursename}} </div>
                	</div>
                	<div class="price">
                		<div class="total-price">{{currency_symbol}}{{sign_up_fee}}</div>
                	</div>
                </li>

                <li class="total">
                	<div class="details">
                		<div class="name"> Enrolment in course {{coursename}} </div>
                		<div class="duratuion">Billed every {{interval_count}} {{interval}} </div>
                	</div>
                	<div class="price">
                		<div class="total-price">{{currency_symbol}}{{renewal_fee}}</div>
                	</div>
                </li>                
           </ul>

           <div class="total">
                <div class="details">
                    <div class="name">Subtotal</div>
                </div>
                <div class="price">
                    <div class="total-price" id="subtotal-amount-{{instanceid}}">{{currency_symbol}}{{subtotal}}</div>
                </div>
           </div>

        {{#enable_coupon_section}}
           <div class="promotion-code">
            <div class="couponcode-wrap">
                <span class="couponcode-text">
                    {{#str}}couponcodedescription, enrol_stripepaymentpro{{/str}}
                </span>
                <div class="stripe-coupon-input-container">
                    <label for="coupon" class="sr-only">{{#str}}enter_coupon_code, enrol_stripepaymentpro{{/str}}</label>
                    <input type="text"
                           id="coupon-{{instanceid}}"
                           class="stripe-coupon-input"
                           placeholder="{{#str}}enter_coupon_code, enrol_stripepaymentpro{{/str}}" />
                    <button id="apply-{{instanceid}}"
                            class="stripe-coupon-apply"
                            type="button"
                            aria-describedby="coupon-message-container">
                        <span class="apply-text">{{#str}}applycode, enrol_stripepaymentpro{{/str}}</span>
                        <span class="apply-spinner hidden">
                            <span class="spinner" aria-hidden="true"></span>
                            {{#str}}coupon_verifying, enrol_stripepaymentpro{{/str}}
                        </span>
                    </button>
                </div>

                <div id="coupon-help" class="coupon-help-text">
                    {{#str}}coupon_help_text, enrol_stripepaymentpro{{/str}}
                </div>

                <!-- Enhanced error and success message container -->
                <div id="coupon-message-container" class="coupon-message-container">
                    <div id="coupon-error" class="coupon-message coupon-error hidden">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <span class="message-text"></span>
                    </div>
                    <div id="coupon-success" class="coupon-message coupon-success hidden">
                        <i class="fa fa-check-circle" aria-hidden="true"></i>
                        <span class="message-text"></span>
                    </div>
                    <div id="coupon-warning" class="coupon-message coupon-warning hidden">
                        <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                        <span class="message-text"></span>
                    </div>
                </div>
                <div id="showmessage-{{instanceid}}"></div>
            </div>
           </div>
        {{/enable_coupon_section}}
            <div id="discountsection-{{instanceid}}" class="discount-section" style="display: none;">
                <div class="discount-row">
                    <div class="details">
                        <div class="name" id="discounttag-{{instanceid}}">Discount</div>
                        <div class="discount-note" id="discountnote-{{instanceid}}">Discount applied</div>
                    </div>
                    <div class="price">
                        <div class="discount-amount" id="discountamountdisplay-{{instanceid}}">-{{currency_symbol}}0.00</div>
                    </div>
                </div>
            </div>
            <div class="total" id="total-{{instanceid}}">
                <div class="details">
                    <div class="name">Total due today</div>
                </div>
                <div class="price">
                    <div id="totalamount-{{instanceid}}" class="total-amount-{{instanceid}}">{{currency_symbol}}{{total_cost}}</div>
                </div>
            <div id="paymentResponse-{{instanceid}}" class="error-message" style="display: none;"></div>
       </div>
       <div id="payment-element-{{instanceid}}"></div>
       <div>
            <div id="buynow-{{instanceid}}">
                <button id="enrolbutton-{{instanceid}}" class="pay-btn" type="button">
                    {{#str}}buy_now, enrol_stripepaymentpro{{/str}}
                </button>
            </div>
        </div>
    </div>
<style>

    .pay-btn {
        margin-top: 18px;
        width: 100%;
        background-color: {{#enrolbtncolor}}{{enrolbtncolor}}{{/enrolbtncolor}}{{^enrolbtncolor}}#0070f3{{/enrolbtncolor}};
        color: white;
        border: none;
        padding: 12px 0;
        font-size: 16px;
        font-weight: bold;
        border-radius: 6px;
        cursor: pointer;
    }

    .pay-btn:hover {
        background-color: {{#enrolbtncolor}}{{enrolbtncolor}}{{/enrolbtncolor}}{{^enrolbtncolor}}#005fd1{{/enrolbtncolor}};
        filter: brightness(0.9);
    }

    .pay-btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }
    .paydetail.wrapper{
      	padding-top:3rem;
        width:60%;  
        margin:auto;         
     }           
    .paydetail .heading{
        font-size: 1rem;
  		margin: 0;
  		color: #6f6f6f;
     }
	.paydetail .price-row{
    	display:flex;
    	align-items: center;
  		gap: 0.5rem;
    }
	.paydetail .price-row .price{
    	font-size:2.375rem;
    }
	.paydetail .price-row .duration{
    	display:flex;
    	flex-direction:column;
    }
	.paydetail ul{
    	padding-top:2.5rem;
    	padding-left:0;
    	border-bottom: 0.063rem solid #eee;
    }
	.paydetail .total{
    	text-decoration:none;
    	padding:0;
    	display:flex;
    	justify-content: space-between;
    	margin-bottom:0.875rem;
    }
	.paydetail .total .details .name,
	.paydetail .total .price .total-price{
    	font-size: 1rem;
  		font-weight: 600;
    }
	.paydetail .total .duratuion{
    	color: #888787;
  		font-size: 0.85rem;
	}
	.paydetail .total .price{
    	text-align:right;
    }

    /* Total section styling */
    .total {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 0.875rem 0 !important;
        border-top: 2px solid #333 !important;
        margin-top: 0.875rem !important;
        margin-bottom: 0 !important;
    }

    .total .details {
        flex: 1;
        margin: 0;
    }

    .total .details .name {
        font-size: 1.125rem !important;
        font-weight: 700 !important;
        margin: 0 !important;
        line-height: 1.2 !important;
    }

    .total .price {
        text-align: right !important;
        margin: 0;
    }

    .total .price div {
        font-size: 1.125rem !important;
        font-weight: 700 !important;
        margin: 0 !important;
        line-height: 1.2 !important;
    }

    /* Discount section styling */
    .discount-section {
        border-top: 1px solid #eee;
        padding-top: 0.875rem;
        margin-bottom: 0.875rem;
    }

    .discount-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.875rem;
    }

    .discount-row .details .name {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
    }

    .discount-row .details .discount-note {
        font-size: 0.875rem;
        color: #6f6f6f;
        margin-top: 0.25rem;
    }

    .discount-row .price .discount-amount {
        font-size: 1rem;
        font-weight: 600;
        color: #00a86b;
    }
	.paydetail .promotion-code{
    	color:#1177d1;
    	font-size:1.1rem;
    	border-bottom: 0.063rem solid #eee;
    }
	.paydetail .promotion-code .couponcode-wrap{
    	width:100%;
    	padding-bottom:0;
    }
	.total.total{
    	padding-top:1rem;
    	margin-bottom:0;
    	color: #00000087;
    	font-weight:500;
    }         

    #card-button-zero {
        background-color: #28a745;
    }

    #card-button-zero:hover {
        background-color: #218838;
    }
    /* Enhanced coupon input styling */
    .stripe-coupon-input-container {
        display: flex;
        gap: 8px;
        margin-top: 8px;
        margin-bottom: 12px;
    }

    .stripe-coupon-input {
        flex: 1;
        padding: 10px 12px;
        border: 2px solid #e1e5e9;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        background-color: #fff;
    }

    .stripe-coupon-input:focus {
        outline: none;
        border-color: #0070f3;
        box-shadow: 0 0 0 3px rgba(0, 112, 243, 0.1);
    }

    .stripe-coupon-input.error {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    .stripe-coupon-input.success {
        border-color: #28a745;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    }

    .stripe-coupon-apply {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 10px 16px;
        font-size: 14px;
        border-radius: 6px;
        cursor: pointer;
        white-space: nowrap;
        min-width: 100px;
        transition: background-color 0.2s ease, transform 0.1s ease;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .stripe-coupon-apply:hover:not(:disabled) {
        background-color: #5a6268;
        transform: translateY(-1px);
    }

    .stripe-coupon-apply:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
        opacity: 0.7;
    }

    .stripe-coupon-apply.loading {
        background-color: #0070f3;
    }

    .apply-spinner {
        display: inline-block;
    }

    /* Enhanced message styling */
    .coupon-message-container {
        margin-top: 8px;
        min-height: 24px;
    }

    .coupon-message {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        animation: slideIn 0.3s ease-out;
    }

    .coupon-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .coupon-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .coupon-warning {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .hidden {
        display: none !important;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Loading animation */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
        margin-right: 6px;
    }

    /* Accessibility and help text */
    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    .coupon-help-text {
        font-size: 12px;
        color: #6c757d;
        margin-top: 4px;
        font-style: italic;
    }

    /* Focus management for better accessibility */
    .stripe-coupon-input:focus,
    #apply:focus {
        outline: 2px solid #0070f3;
        outline-offset: 2px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .coupon-error {
            background-color: #ffffff;
            color: #000000;
            border: 2px solid #dc3545;
        }

        .coupon-success {
            background-color: #ffffff;
            color: #000000;
            border: 2px solid #28a745;
        }

        .coupon-warning {
            background-color: #ffffff;
            color: #000000;
            border: 2px solid #ffc107;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .coupon-message,
        .spinner {
            animation: none;
        }

        #apply {
            transition: none;
        }
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .stripe-coupon-input-container {
            flex-direction: column;
            gap: 12px;
        }

        .stripe-coupon-input,
        #apply {
            width: 100%;
        }

        #apply {
            min-width: auto;
            padding: 12px 16px;
        }
    }
    </style>